// Local storage service for storing trips when MongoDB is not available

// Generate a unique ID for trips
const generateId = () => {
  return Math.random().toString(36).substring(2, 15) +
         Math.random().toString(36).substring(2, 15);
};

// In-memory storage for server-side rendering
let memoryStorage: Record<string, any[]> = { trips: [] };

// Get all trips from storage
export const getTrips = () => {
  if (typeof window === 'undefined') {
    // Server-side: use memory storage
    return memoryStorage.trips || [];
  }

  try {
    // Client-side: use localStorage
    const tripsJson = localStorage.getItem('trips');
    return tripsJson ? JSON.parse(tripsJson) : [];
  } catch (error) {
    console.error('Error getting trips from storage:', error);
    return [];
  }
};

// Get a specific trip by ID
export const getTripById = (id: string) => {
  try {
    const trips = getTrips();
    return trips.find((trip: any) => trip._id === id) || null;
  } catch (error) {
    console.error('Error getting trip from storage:', error);
    return null;
  }
};

// Save a trip to storage
export const saveTrip = (tripData: any) => {
  try {
    const trips = getTrips();
    const newTrip = {
      ...tripData,
      _id: generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    trips.push(newTrip);

    if (typeof window === 'undefined') {
      // Server-side: use memory storage
      memoryStorage.trips = trips;
    } else {
      // Client-side: use localStorage
      localStorage.setItem('trips', JSON.stringify(trips));
      // Trigger storage event for other tabs
      window.dispatchEvent(new Event('storage'));
    }

    return newTrip;
  } catch (error) {
    console.error('Error saving trip to storage:', error);
    return null;
  }
};

// Delete a trip from storage
export const deleteTrip = (id: string) => {
  try {
    const trips = getTrips();
    const filteredTrips = trips.filter((trip: any) => trip._id !== id);

    if (typeof window === 'undefined') {
      // Server-side: use memory storage
      memoryStorage.trips = filteredTrips;
    } else {
      // Client-side: use localStorage
      localStorage.setItem('trips', JSON.stringify(filteredTrips));
      // Trigger storage event for other tabs
      window.dispatchEvent(new Event('storage'));
    }

    return true;
  } catch (error) {
    console.error('Error deleting trip from storage:', error);
    return false;
  }
};
