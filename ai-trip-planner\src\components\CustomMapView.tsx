"use client";

import { useState } from 'react';
import { MapLocation } from './TripMap';

interface CustomMapViewProps {
  destination: string;
  locations: MapLocation[];
  budget: number;
}

export default function CustomMapView({ destination, locations, budget }: CustomMapViewProps) {
  const [selectedLocation, setSelectedLocation] = useState<MapLocation | null>(null);
  
  // Group locations by type
  const hotels = locations.filter(loc => loc.type === 'hotel');
  const restaurants = locations.filter(loc => loc.type === 'restaurant');
  const attractions = locations.filter(loc => loc.type === 'attraction');
  
  // Helper function to determine if a place is within budget
  const isWithinBudget = (priceLevel?: number) => {
    if (!priceLevel) return true;
    return priceLevel <= Math.ceil(budget / 1000);
  };
  
  return (
    <div className="w-full rounded-lg overflow-hidden shadow-md bg-white dark:bg-gray-800">
      <div className="p-6 border-b border-gray-200 dark:border-gray-700 bg-indigo-50 dark:bg-indigo-900/20">
        <div className="flex items-center justify-center mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-indigo-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
          </svg>
          <h3 className="text-xl font-bold text-gray-800 dark:text-white">Map View for {destination}</h3>
        </div>
        
        <div className="grid grid-cols-3 gap-4 text-center">
          <div className="bg-white dark:bg-gray-700 p-3 rounded-lg shadow-sm">
            <div className="flex items-center justify-center mb-2">
              <span className="w-4 h-4 bg-blue-500 rounded-full mr-2"></span>
              <span className="font-medium text-gray-800 dark:text-white">Hotels</span>
            </div>
            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{hotels.length}</p>
          </div>
          
          <div className="bg-white dark:bg-gray-700 p-3 rounded-lg shadow-sm">
            <div className="flex items-center justify-center mb-2">
              <span className="w-4 h-4 bg-red-500 rounded-full mr-2"></span>
              <span className="font-medium text-gray-800 dark:text-white">Restaurants</span>
            </div>
            <p className="text-2xl font-bold text-red-600 dark:text-red-400">{restaurants.length}</p>
          </div>
          
          <div className="bg-white dark:bg-gray-700 p-3 rounded-lg shadow-sm">
            <div className="flex items-center justify-center mb-2">
              <span className="w-4 h-4 bg-green-500 rounded-full mr-2"></span>
              <span className="font-medium text-gray-800 dark:text-white">Attractions</span>
            </div>
            <p className="text-2xl font-bold text-green-600 dark:text-green-400">{attractions.length}</p>
          </div>
        </div>
      </div>
      
      {/* Visual Map Representation */}
      <div className="p-4 bg-gray-50 dark:bg-gray-900 relative" style={{ height: '400px' }}>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-24 h-24 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center z-10">
            <span className="text-indigo-600 dark:text-indigo-400 font-bold">{destination.split(',')[0]}</span>
          </div>
        </div>
        
        {/* Hotels */}
        {hotels.map((hotel, index) => (
          <div 
            key={hotel.id}
            className={`absolute cursor-pointer transform transition-all duration-200 hover:scale-110 ${
              selectedLocation?.id === hotel.id ? 'scale-110 z-20' : 'z-10'
            }`}
            style={{
              top: `${20 + (index * 15) % 60}%`,
              left: `${15 + (index * 20) % 70}%`,
            }}
            onClick={() => setSelectedLocation(hotel)}
          >
            <div className="relative">
              <div className="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center shadow-lg">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              {selectedLocation?.id === hotel.id && (
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-3 z-30">
                  <h4 className="font-medium text-gray-800 dark:text-white text-sm">{hotel.name}</h4>
                  {hotel.rating && (
                    <div className="flex items-center mt-1">
                      <span className="text-yellow-500 mr-1">★</span>
                      <span className="text-xs text-gray-600 dark:text-gray-300">{hotel.rating.toFixed(1)}</span>
                    </div>
                  )}
                  {hotel.priceLevel !== undefined && (
                    <div className="mt-1">
                      <span className={`text-xs ${isWithinBudget(hotel.priceLevel) ? 'text-green-600' : 'text-yellow-600'}`}>
                        {Array(hotel.priceLevel).fill('$').join('')}
                      </span>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        ))}
        
        {/* Restaurants */}
        {restaurants.map((restaurant, index) => (
          <div 
            key={restaurant.id}
            className={`absolute cursor-pointer transform transition-all duration-200 hover:scale-110 ${
              selectedLocation?.id === restaurant.id ? 'scale-110 z-20' : 'z-10'
            }`}
            style={{
              top: `${30 + (index * 12) % 50}%`,
              left: `${30 + (index * 15) % 60}%`,
            }}
            onClick={() => setSelectedLocation(restaurant)}
          >
            <div className="relative">
              <div className="w-10 h-10 rounded-full bg-red-500 flex items-center justify-center shadow-lg">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              {selectedLocation?.id === restaurant.id && (
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-3 z-30">
                  <h4 className="font-medium text-gray-800 dark:text-white text-sm">{restaurant.name}</h4>
                  {restaurant.rating && (
                    <div className="flex items-center mt-1">
                      <span className="text-yellow-500 mr-1">★</span>
                      <span className="text-xs text-gray-600 dark:text-gray-300">{restaurant.rating.toFixed(1)}</span>
                    </div>
                  )}
                  {restaurant.priceLevel !== undefined && (
                    <div className="mt-1">
                      <span className={`text-xs ${isWithinBudget(restaurant.priceLevel) ? 'text-green-600' : 'text-yellow-600'}`}>
                        {Array(restaurant.priceLevel).fill('$').join('')}
                      </span>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        ))}
        
        {/* Attractions */}
        {attractions.map((attraction, index) => (
          <div 
            key={attraction.id}
            className={`absolute cursor-pointer transform transition-all duration-200 hover:scale-110 ${
              selectedLocation?.id === attraction.id ? 'scale-110 z-20' : 'z-10'
            }`}
            style={{
              top: `${50 + (index * 10) % 40}%`,
              left: `${50 + (index * 12) % 45}%`,
            }}
            onClick={() => setSelectedLocation(attraction)}
          >
            <div className="relative">
              <div className="w-10 h-10 rounded-full bg-green-500 flex items-center justify-center shadow-lg">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
                </svg>
              </div>
              {selectedLocation?.id === attraction.id && (
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-3 z-30">
                  <h4 className="font-medium text-gray-800 dark:text-white text-sm">{attraction.name}</h4>
                  {attraction.rating && (
                    <div className="flex items-center mt-1">
                      <span className="text-yellow-500 mr-1">★</span>
                      <span className="text-xs text-gray-600 dark:text-gray-300">{attraction.rating.toFixed(1)}</span>
                    </div>
                  )}
                  {attraction.priceLevel !== undefined && (
                    <div className="mt-1">
                      <span className={`text-xs ${isWithinBudget(attraction.priceLevel) ? 'text-green-600' : 'text-yellow-600'}`}>
                        {Array(attraction.priceLevel).fill('$').join('')}
                      </span>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        ))}
        
        {/* Background grid lines */}
        <div className="absolute inset-0 grid grid-cols-4 grid-rows-4 pointer-events-none">
          {[...Array(4)].map((_, i) => (
            <div key={`col-${i}`} className="border-r border-gray-200 dark:border-gray-700 h-full"></div>
          ))}
          {[...Array(4)].map((_, i) => (
            <div key={`row-${i}`} className="border-b border-gray-200 dark:border-gray-700 w-full"></div>
          ))}
        </div>
      </div>
      
      {/* Location List */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <h4 className="font-medium text-gray-800 dark:text-white mb-3">Nearby Places</h4>
        <div className="space-y-3 max-h-60 overflow-y-auto custom-scrollbar">
          {locations.slice(0, 6).map(location => (
            <div 
              key={location.id} 
              className={`flex items-center p-2 rounded-lg cursor-pointer transition-colors duration-200 ${
                selectedLocation?.id === location.id 
                  ? 'bg-indigo-50 dark:bg-indigo-900/20' 
                  : 'bg-gray-50 dark:bg-gray-900 hover:bg-gray-100 dark:hover:bg-gray-800'
              }`}
              onClick={() => setSelectedLocation(location)}
            >
              <div className="flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-3">
                {location.type === 'hotel' && (
                  <span className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </span>
                )}
                {location.type === 'restaurant' && (
                  <span className="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-600 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </span>
                )}
                {location.type === 'attraction' && (
                  <span className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
                    </svg>
                  </span>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-800 dark:text-white truncate">{location.name}</p>
                {location.rating && (
                  <div className="flex items-center">
                    <span className="text-yellow-500 mr-1 text-xs">★</span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">{location.rating.toFixed(1)}</span>
                  </div>
                )}
              </div>
              {location.priceLevel !== undefined && (
                <span className={`ml-2 px-2 py-1 text-xs rounded-full ${isWithinBudget(location.priceLevel) ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'}`}>
                  {Array(location.priceLevel).fill('$').join('')}
                </span>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
