import mongoose, { Schema, Document } from 'mongoose';

// Define interfaces for our Trip model
export interface ILocation {
  id: string;
  type: 'hotel' | 'restaurant' | 'attraction';
  name: string;
  position: {
    lat: number;
    lng: number;
  };
  priceLevel?: number;
  rating?: number;
  address?: string;
  photoUrl?: string;
}

export interface ICost {
  activities: number;
  accommodation: number;
  meals: number;
  transportation: number;
}

export interface ITripDay {
  day: number;
  activities: string[];
  accommodation: string;
  meals: string[];
  costs: ICost;
}

export interface ITrip extends Document {
  userId?: string;
  destination: string;
  summary: string;
  totalCost: number;
  budget: number;
  duration: number;
  travelers: number;
  startingLocation: string;
  interests: string;
  itinerary: ITripDay[];
  locations: ILocation[];
  createdAt: Date;
  updatedAt: Date;
}

// Create the Trip schema
const TripSchema: Schema = new Schema(
  {
    userId: { type: String },
    destination: { type: String, required: true },
    summary: { type: String, required: true },
    totalCost: { type: Number, required: true },
    budget: { type: Number, required: true },
    duration: { type: Number, required: true },
    travelers: { type: Number, required: true },
    startingLocation: { type: String, required: true },
    interests: { type: String },
    itinerary: [
      {
        day: { type: Number, required: true },
        activities: [{ type: String }],
        accommodation: { type: String },
        meals: [{ type: String }],
        costs: {
          activities: { type: Number },
          accommodation: { type: Number },
          meals: { type: Number },
          transportation: { type: Number }
        }
      }
    ],
    locations: [
      {
        id: { type: String, required: true },
        type: { type: String, enum: ['hotel', 'restaurant', 'attraction'], required: true },
        name: { type: String, required: true },
        position: {
          lat: { type: Number, required: true },
          lng: { type: Number, required: true }
        },
        priceLevel: { type: Number },
        rating: { type: Number },
        address: { type: String },
        photoUrl: { type: String }
      }
    ]
  },
  { timestamps: true }
);

// Create and export the Trip model
export default mongoose.models.Trip || mongoose.model<ITrip>('Trip', TripSchema);
