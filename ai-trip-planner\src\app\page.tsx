"use client";

import { useState, useEffect } from 'react';
import TripForm, { TripFormData } from '@/components/TripForm';
import TripResults, { TripSuggestion } from '@/components/TripResults';
import { MapLocation } from '@/components/TripMap';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

export default function Home() {
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [tripSuggestion, setTripSuggestion] = useState<TripSuggestion | null>(null);
  const [locations, setLocations] = useState<MapLocation[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [savedTrips, setSavedTrips] = useState<any[]>([]);
  const [currentFormData, setCurrentFormData] = useState<TripFormData | null>(null);

  // Fetch saved trips when component mounts
  useEffect(() => {
    fetchSavedTrips();
  }, []);

  // Add event listener for storage changes (for local storage fallback)
  useEffect(() => {
    const handleStorageChange = () => {
      fetchSavedTrips();
    };

    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // Fetch saved trips from the API
  const fetchSavedTrips = async () => {
    try {
      const response = await fetch('/api/trips');
      if (response.ok) {
        const data = await response.json();
        setSavedTrips(data.trips);
      }
    } catch (error) {
      console.error('Error fetching saved trips:', error);
    }
  };

  // Save the current trip to MongoDB
  const saveTrip = async () => {
    if (!tripSuggestion || !currentFormData) return;

    setIsSaving(true);
    try {
      const response = await fetch('/api/trips', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          suggestion: tripSuggestion,
          locations,
          formData: currentFormData
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save trip');
      }

      const data = await response.json();
      toast.success('Trip saved successfully!');

      // Refresh the saved trips list
      fetchSavedTrips();
    } catch (err) {
      console.error('Error saving trip:', err);
      toast.error('Failed to save trip. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSubmit = async (formData: TripFormData) => {
    setIsLoading(true);
    setError(null);
    setCurrentFormData(formData); // Store the form data for later use

    try {
      const response = await fetch('/api/trip-suggestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error('Failed to generate trip suggestion');
      }

      const data = await response.json();
      setTripSuggestion(data.suggestion);
      setLocations(data.locations || []);
    } catch (err) {
      console.error('Error:', err);
      setError('Failed to generate trip suggestion. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen hero-pattern dark:bg-gray-900">
      <div className="hero-gradient py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <header className="text-center mb-10 animate-fade-in">
            <h1 className="text-5xl font-bold text-white mb-4 drop-shadow-md">
              AI Trip Planner
            </h1>
            <p className="text-xl text-white/90 max-w-2xl mx-auto">
              Discover your perfect getaway with personalized recommendations based on your budget and preferences
            </p>
          </header>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-8 -mt-8">

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="animate-fade-in" style={{animationDelay: '0.2s'}}>
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden card-hover">
              <div className="bg-indigo-600 px-6 py-4">
                <h2 className="text-xl font-bold text-white">Start Planning Your Trip</h2>
              </div>
              <div className="p-6">
                <TripForm onSubmit={handleSubmit} isLoading={isLoading} />

                {error && (
                  <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-md">
                    {error}
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="animate-fade-in" style={{animationDelay: '0.4s'}}>
            {isLoading ? (
              <div className="flex items-center justify-center h-96 bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                <div className="text-center">
                  <div className="pulse rounded-full h-16 w-16 border-4 border-indigo-600 mx-auto mb-6"></div>
                  <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">Creating Your Dream Trip</h3>
                  <p className="text-gray-600 dark:text-gray-300">Our AI is crafting a personalized travel experience just for you...</p>
                </div>
              </div>
            ) : tripSuggestion ? (
              <>
                <TripResults suggestion={tripSuggestion} locations={locations} />
                <div className="mt-4 flex justify-center">
                  <button
                    onClick={saveTrip}
                    disabled={isSaving}
                    className="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg shadow-md flex items-center justify-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSaving ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Saving Trip...
                      </>
                    ) : (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                        </svg>
                        Save This Trip
                      </>
                    )}
                  </button>
                </div>
              </>
            ) : (
              <div className="flex items-center justify-center h-96 bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 bg-[url('https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80')] bg-cover bg-center">
                <div className="text-center bg-black/50 p-6 rounded-lg backdrop-blur-sm">
                  <h3 className="text-2xl font-bold text-white mb-4">Your Adventure Awaits</h3>
                  <p className="text-white/90 mb-2 text-lg">
                    Fill out the form to get AI-powered trip suggestions
                  </p>
                  <p className="text-white/80">
                    Discover hotels, restaurants, and attractions tailored to your budget
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Saved Trips Section */}
        {savedTrips.length > 0 && (
          <div className="mt-16 animate-fade-in">
            <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-6 text-center">
              Your Saved Trips
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {savedTrips.map((trip) => (
                <div key={trip._id} className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden card-hover">
                  <div className="bg-gradient-to-r from-indigo-600 to-purple-600 p-4 text-white">
                    <h3 className="font-bold text-lg">{trip.destination}</h3>
                    <div className="flex justify-between items-center mt-1">
                      <span className="text-sm">{trip.duration} days</span>
                      <span className="text-sm">${trip.totalCost}</span>
                    </div>
                  </div>
                  <div className="p-4">
                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-3 line-clamp-2">
                      {trip.summary}
                    </p>
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(trip.createdAt).toLocaleDateString()}
                      </span>
                      <button
                        onClick={() => window.location.href = `/trips/${trip._id}`}
                        className="text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300 text-sm font-medium"
                      >
                        View Details
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <footer className="mt-20 text-center">
          <div className="py-8 border-t border-gray-200 dark:border-gray-800">
            <div className="flex justify-center space-x-6 mb-4">
              <a href="#" className="text-gray-500 hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400">
                <span className="sr-only">Facebook</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
                </svg>
              </a>
              <a href="#" className="text-gray-500 hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400">
                <span className="sr-only">Instagram</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path fillRule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clipRule="evenodd" />
                </svg>
              </a>
              <a href="#" className="text-gray-500 hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400">
                <span className="sr-only">Twitter</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                </svg>
              </a>
            </div>
            <p className="text-gray-600 dark:text-gray-400 font-medium">© {new Date().getFullYear()} AI Trip Planner. All rights reserved.</p>
            <p className="mt-2 text-gray-500 dark:text-gray-500">Powered by AI to create personalized travel experiences.</p>
          </div>
        </footer>

        {/* Toast notifications */}
        <ToastContainer position="bottom-right" autoClose={3000} />

      </div>
    </div>
  );
}
