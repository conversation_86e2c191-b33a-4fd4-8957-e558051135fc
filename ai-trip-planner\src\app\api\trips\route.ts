import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Trip from '@/models/Trip';
import * as localStorageService from '@/lib/localStorageService';

// GET handler to retrieve all trips
export async function GET() {
  try {
    // Try to connect to MongoDB first
    try {
      await dbConnect();
      const trips = await Trip.find({}).sort({ createdAt: -1 });
      return NextResponse.json({ trips }, { status: 200 });
    } catch (mongoError) {
      console.error('MongoDB error, falling back to local storage:', mongoError);

      // Fallback to local storage
      const trips = localStorageService.getTrips();
      return NextResponse.json({ trips }, { status: 200 });
    }
  } catch (error) {
    console.error('Error fetching trips:', error);
    return NextResponse.json(
      { error: 'Failed to fetch trips' },
      { status: 500 }
    );
  }
}

// POST handler to save a new trip
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // Prepare trip data
    const tripData = {
      destination: data.suggestion.destination,
      summary: data.suggestion.summary,
      totalCost: data.suggestion.totalCost,
      budget: data.formData.budget,
      duration: data.formData.duration,
      travelers: data.formData.travelers,
      startingLocation: data.formData.startingLocation,
      interests: data.formData.interests,
      itinerary: data.suggestion.itinerary,
      locations: data.locations
    };

    // Try to save to MongoDB first
    try {
      await dbConnect();
      const trip = new Trip(tripData);
      const savedTrip = await trip.save();
      return NextResponse.json({ trip: savedTrip }, { status: 201 });
    } catch (mongoError) {
      console.error('MongoDB error, falling back to local storage:', mongoError);

      // Fallback to local storage
      const savedTrip = localStorageService.saveTrip(tripData);
      if (savedTrip) {
        return NextResponse.json({ trip: savedTrip }, { status: 201 });
      } else {
        throw new Error('Failed to save to local storage');
      }
    }
  } catch (error) {
    console.error('Error saving trip:', error);
    return NextResponse.json(
      { error: 'Failed to save trip' },
      { status: 500 }
    );
  }
}
