"use client";

import { useState } from 'react';
import TripMap, { MapLocation } from './TripMap';
import PlaceRecommendations from './PlaceRecommendations';

export interface TripDay {
  day: number;
  activities: string[];
  accommodation: string;
  meals: string[];
  costs: {
    activities: number;
    accommodation: number;
    meals: number;
    transportation: number;
  };
}

export interface TripSuggestion {
  destination: string;
  summary: string;
  totalCost: number;
  itinerary: TripDay[];
}

interface TripResultsProps {
  suggestion: TripSuggestion | null;
  locations?: MapLocation[];
}

export default function TripResults({ suggestion, locations = [] }: TripResultsProps) {
  const [activeDay, setActiveDay] = useState(1);
  const [activeTab, setActiveTab] = useState<'itinerary' | 'map' | 'places'>('itinerary');

  if (!suggestion) return null;

  const { destination, summary, totalCost, itinerary } = suggestion;
  const budget = totalCost;

  return (
    <div className="w-full max-w-4xl mx-auto bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden animate-fade-in">
      <div className="bg-gradient-to-r from-indigo-600 to-purple-600 p-6 text-white">
        <h2 className="text-3xl font-bold text-center mb-2 drop-shadow-sm">
          Your Trip to {destination}
        </h2>

        <div className="text-center mb-4">
          <span className="inline-block bg-white/20 backdrop-blur-sm px-4 py-1.5 rounded-full text-base font-medium shadow-sm">
            Estimated Cost: ${totalCost}
          </span>
        </div>

        <p className="text-white/90 mb-2 text-center max-w-2xl mx-auto">
          {summary}
        </p>
      </div>

      <div className="p-6">

      {/* Tabs */}
      <div className="flex space-x-1 mb-6 bg-gray-100 dark:bg-gray-900 p-1 rounded-lg">
        <button
          className={`flex-1 px-4 py-2.5 text-sm font-medium rounded-md transition-all duration-200 ${activeTab === 'itinerary'
            ? 'bg-white dark:bg-gray-800 text-indigo-600 dark:text-indigo-400 shadow-sm'
            : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-white/50 dark:hover:bg-gray-800/50'}`}
          onClick={() => setActiveTab('itinerary')}
        >
          <div className="flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            Itinerary
          </div>
        </button>
        <button
          className={`flex-1 px-4 py-2.5 text-sm font-medium rounded-md transition-all duration-200 ${activeTab === 'map'
            ? 'bg-white dark:bg-gray-800 text-indigo-600 dark:text-indigo-400 shadow-sm'
            : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-white/50 dark:hover:bg-gray-800/50'}`}
          onClick={() => setActiveTab('map')}
        >
          <div className="flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
            </svg>
            Map
          </div>
        </button>
        <button
          className={`flex-1 px-4 py-2.5 text-sm font-medium rounded-md transition-all duration-200 ${activeTab === 'places'
            ? 'bg-white dark:bg-gray-800 text-indigo-600 dark:text-indigo-400 shadow-sm'
            : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-white/50 dark:hover:bg-gray-800/50'}`}
          onClick={() => setActiveTab('places')}
        >
          <div className="flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
            Places to Visit
          </div>
        </button>
      </div>

      {/* Itinerary Tab */}
      {activeTab === 'itinerary' && (
        <div className="mb-6">
          <div className="flex overflow-x-auto space-x-2 mb-4 pb-2">
            {itinerary.map((day) => (
              <button
                key={day.day}
                onClick={() => setActiveDay(day.day)}
                className={`px-4 py-2 rounded-md text-sm font-medium whitespace-nowrap ${
                  activeDay === day.day
                    ? 'bg-indigo-600 text-white'
                    : 'bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                Day {day.day}
              </button>
            ))}
          </div>

          {itinerary.map((day) => (
            <div
              key={day.day}
              className={`bg-gray-50 dark:bg-gray-900 rounded-lg p-4 ${
                activeDay === day.day ? 'block' : 'hidden'
              }`}
            >
              <h4 className="text-lg font-semibold mb-3 text-gray-800 dark:text-white">
                Day {day.day}
              </h4>

              <div className="space-y-4">
                <div>
                  <h5 className="font-medium text-gray-700 dark:text-gray-300 mb-1">Activities</h5>
                  <ul className="list-disc list-inside text-gray-600 dark:text-gray-400 space-y-1">
                    {day.activities.map((activity, index) => (
                      <li key={index}>{activity}</li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h5 className="font-medium text-gray-700 dark:text-gray-300 mb-1">Accommodation</h5>
                  <p className="text-gray-600 dark:text-gray-400">{day.accommodation}</p>
                </div>

                <div>
                  <h5 className="font-medium text-gray-700 dark:text-gray-300 mb-1">Meals</h5>
                  <ul className="list-disc list-inside text-gray-600 dark:text-gray-400 space-y-1">
                    {day.meals.map((meal, index) => (
                      <li key={index}>{meal}</li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h5 className="font-medium text-gray-700 dark:text-gray-300 mb-1">Costs</h5>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="flex justify-between">
                      <span>Activities:</span>
                      <span>${day.costs.activities}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Accommodation:</span>
                      <span>${day.costs.accommodation}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Meals:</span>
                      <span>${day.costs.meals}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Transportation:</span>
                      <span>${day.costs.transportation}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Map Tab */}
      {activeTab === 'map' && (
        <div className="mb-6">
          <TripMap
            destination={destination}
            locations={locations}
            budget={budget}
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-2 text-center">
            Map shows hotels, restaurants, and attractions in {destination}.
            Click on markers for more details.
          </p>
        </div>
      )}

      {/* Places Tab */}
      {activeTab === 'places' && (
        <div className="mb-6">
          <PlaceRecommendations
            locations={locations}
            budget={budget}
          />
        </div>
      )}

      <div className="mt-6 text-center border-t border-gray-100 dark:border-gray-800 pt-4">
        <p className="text-sm text-gray-500 dark:text-gray-400">
          This is an AI-generated suggestion based on your preferences. Actual costs and availability may vary.
        </p>
      </div>
      </div>
    </div>
  );
}
