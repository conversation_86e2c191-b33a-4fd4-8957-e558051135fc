"use client";

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import TripResults from '@/components/TripResults';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import * as localStorageService from '@/lib/localStorageService';

export default function TripDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [trip, setTrip] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const { id } = params;

  useEffect(() => {
    const fetchTrip = async () => {
      try {
        // Try API first
        try {
          const response = await fetch(`/api/trips/${id}`);
          if (response.ok) {
            const data = await response.json();
            if (data.trip) {
              setTrip(data.trip);
              setIsLoading(false);
              return;
            }
          }
        } catch (apiError) {
          console.error('API error, trying local storage:', apiError);
        }

        // Fallback to local storage
        const localTrip = localStorageService.getTripById(id as string);
        if (localTrip) {
          setTrip(localTrip);
        } else {
          toast.error('Trip not found');
        }
      } catch (error) {
        console.error('Error fetching trip:', error);
        toast.error('Failed to load trip details');
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchTrip();
    }
  }, [id]);

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this trip?')) {
      setIsDeleting(true);
      try {
        // Try API first
        try {
          const response = await fetch(`/api/trips/${id}`, {
            method: 'DELETE',
          });

          if (response.ok) {
            toast.success('Trip deleted successfully');
            setTimeout(() => {
              router.push('/');
            }, 2000);
            return;
          }
        } catch (apiError) {
          console.error('API error, trying local storage:', apiError);
        }

        // Fallback to local storage
        const deleted = localStorageService.deleteTrip(id as string);
        if (deleted) {
          toast.success('Trip deleted successfully');
          setTimeout(() => {
            router.push('/');
          }, 2000);
        } else {
          throw new Error('Failed to delete trip');
        }
      } catch (error) {
        console.error('Error deleting trip:', error);
        toast.error('Failed to delete trip');
        setIsDeleting(false);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
              <p className="text-gray-600 dark:text-gray-300">Loading trip details...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!trip) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Trip Not Found</h2>
              <p className="text-gray-600 dark:text-gray-300 mb-6">The trip you're looking for doesn't exist or has been deleted.</p>
              <button
                onClick={() => router.push('/')}
                className="px-6 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg shadow-md"
              >
                Back to Home
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Format trip data for TripResults component
  const tripSuggestion = {
    destination: trip.destination,
    summary: trip.summary,
    totalCost: trip.totalCost,
    itinerary: trip.itinerary
  };

  return (
    <div className="min-h-screen hero-pattern dark:bg-gray-900">
      <div className="hero-gradient py-8 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="flex justify-between items-center mb-6">
            <button
              onClick={() => router.push('/')}
              className="text-white flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to Home
            </button>
            <button
              onClick={handleDelete}
              disabled={isDeleting}
              className="text-white bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isDeleting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Deleting...
                </>
              ) : (
                <>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  Delete Trip
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-8 -mt-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
          <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Trip to {trip.destination}
              </h1>
              <p className="text-gray-600 dark:text-gray-300">
                {trip.duration} days • {trip.travelers} {trip.travelers === 1 ? 'traveler' : 'travelers'} • Budget: ${trip.budget}
              </p>
            </div>
            <div className="mt-4 md:mt-0">
              <span className="inline-block bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200 px-3 py-1 rounded-full text-sm font-medium">
                Created on {new Date(trip.createdAt).toLocaleDateString()}
              </span>
            </div>
          </div>

          <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">Trip Details</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">{trip.summary}</p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg">
                <h3 className="font-medium text-gray-800 dark:text-white mb-2">Starting Location</h3>
                <p className="text-gray-600 dark:text-gray-400">{trip.startingLocation}</p>
              </div>
              <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg">
                <h3 className="font-medium text-gray-800 dark:text-white mb-2">Interests</h3>
                <p className="text-gray-600 dark:text-gray-400">{trip.interests || 'Not specified'}</p>
              </div>
              <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg">
                <h3 className="font-medium text-gray-800 dark:text-white mb-2">Total Cost</h3>
                <p className="text-gray-600 dark:text-gray-400">${trip.totalCost}</p>
              </div>
            </div>
          </div>
        </div>

        <TripResults suggestion={tripSuggestion} locations={trip.locations} />
      </div>

      <ToastContainer position="bottom-right" autoClose={3000} />
    </div>
  );
}
