"use client";

import { useState } from 'react';
import { MapLocation } from './TripMap';

interface PlaceRecommendationsProps {
  locations: MapLocation[];
  budget: number;
}

export default function PlaceRecommendations({ locations, budget }: PlaceRecommendationsProps) {
  const [activeTab, setActiveTab] = useState<'hotels' | 'restaurants' | 'attractions'>('hotels');

  // Filter locations by type
  const hotels = locations.filter(loc => loc.type === 'hotel');
  const restaurants = locations.filter(loc => loc.type === 'restaurant');
  const attractions = locations.filter(loc => loc.type === 'attraction');

  // Determine if a place is within budget
  const isWithinBudget = (priceLevel?: number) => {
    if (!priceLevel) return true;
    return priceLevel <= Math.ceil(budget / 1000);
  };

  // Get price level display
  const getPriceLevel = (priceLevel?: number) => {
    if (!priceLevel) return 'N/A';
    return Array(priceLevel).fill('$').join('');
  };

  return (
    <div className="w-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
      {/* Tabs */}
      <div className="flex space-x-1 p-2 bg-gray-100 dark:bg-gray-900 rounded-lg mx-2 mt-2">
        <button
          className={`flex-1 py-2 px-3 text-sm font-medium rounded-md transition-all duration-200 ${
            activeTab === 'hotels'
              ? 'bg-white dark:bg-gray-800 text-indigo-600 dark:text-indigo-400 shadow-sm'
              : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-white/50 dark:hover:bg-gray-800/50'
          }`}
          onClick={() => setActiveTab('hotels')}
        >
          <div className="flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
            Hotels
          </div>
        </button>
        <button
          className={`flex-1 py-2 px-3 text-sm font-medium rounded-md transition-all duration-200 ${
            activeTab === 'restaurants'
              ? 'bg-white dark:bg-gray-800 text-indigo-600 dark:text-indigo-400 shadow-sm'
              : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-white/50 dark:hover:bg-gray-800/50'
          }`}
          onClick={() => setActiveTab('restaurants')}
        >
          <div className="flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Cafes & Restaurants
          </div>
        </button>
        <button
          className={`flex-1 py-2 px-3 text-sm font-medium rounded-md transition-all duration-200 ${
            activeTab === 'attractions'
              ? 'bg-white dark:bg-gray-800 text-indigo-600 dark:text-indigo-400 shadow-sm'
              : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-white/50 dark:hover:bg-gray-800/50'
          }`}
          onClick={() => setActiveTab('attractions')}
        >
          <div className="flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
            </svg>
            Attractions
          </div>
        </button>
      </div>

      {/* Content */}
      <div className="p-4 custom-scrollbar" style={{maxHeight: '600px', overflowY: 'auto'}}>
        {/* Hotels Tab */}
        {activeTab === 'hotels' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-3">
              Recommended Hotels
            </h3>

            {hotels.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400">No hotel recommendations available.</p>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {hotels.map(hotel => (
                  <div
                    key={hotel.id}
                    className={`border rounded-lg overflow-hidden ${
                      isWithinBudget(hotel.priceLevel)
                        ? 'border-green-200 dark:border-green-900'
                        : 'border-yellow-200 dark:border-yellow-900'
                    }`}
                  >
                    {hotel.photoUrl ? (
                      <div className="h-32 overflow-hidden">
                        <img
                          src={hotel.photoUrl}
                          alt={hotel.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className="h-32 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                        <span className="text-gray-400 dark:text-gray-500">No image</span>
                      </div>
                    )}

                    <div className="p-3">
                      <div className="flex justify-between items-start mb-1">
                        <h4 className="font-medium text-gray-800 dark:text-white">{hotel.name}</h4>
                        <span className={`text-sm font-medium ${
                          isWithinBudget(hotel.priceLevel)
                            ? 'text-green-600 dark:text-green-400'
                            : 'text-yellow-600 dark:text-yellow-400'
                        }`}>
                          {getPriceLevel(hotel.priceLevel)}
                        </span>
                      </div>

                      {hotel.rating && (
                        <div className="flex items-center mb-1">
                          <span className="text-yellow-500 mr-1">★</span>
                          <span className="text-sm text-gray-600 dark:text-gray-300">{hotel.rating.toFixed(1)}</span>
                        </div>
                      )}

                      {hotel.address && (
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{hotel.address}</p>
                      )}

                      <div className="mt-2">
                        <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                          isWithinBudget(hotel.priceLevel)
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                        }`}>
                          {isWithinBudget(hotel.priceLevel) ? 'Within Budget' : 'Above Budget'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Restaurants Tab */}
        {activeTab === 'restaurants' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-3">
              Recommended Cafes & Restaurants
            </h3>

            {restaurants.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400">No restaurant recommendations available.</p>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {restaurants.map(restaurant => (
                  <div
                    key={restaurant.id}
                    className={`border rounded-lg overflow-hidden ${
                      isWithinBudget(restaurant.priceLevel)
                        ? 'border-green-200 dark:border-green-900'
                        : 'border-yellow-200 dark:border-yellow-900'
                    }`}
                  >
                    {restaurant.photoUrl ? (
                      <div className="h-32 overflow-hidden">
                        <img
                          src={restaurant.photoUrl}
                          alt={restaurant.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className="h-32 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                        <span className="text-gray-400 dark:text-gray-500">No image</span>
                      </div>
                    )}

                    <div className="p-3">
                      <div className="flex justify-between items-start mb-1">
                        <h4 className="font-medium text-gray-800 dark:text-white">{restaurant.name}</h4>
                        <span className={`text-sm font-medium ${
                          isWithinBudget(restaurant.priceLevel)
                            ? 'text-green-600 dark:text-green-400'
                            : 'text-yellow-600 dark:text-yellow-400'
                        }`}>
                          {getPriceLevel(restaurant.priceLevel)}
                        </span>
                      </div>

                      {restaurant.rating && (
                        <div className="flex items-center mb-1">
                          <span className="text-yellow-500 mr-1">★</span>
                          <span className="text-sm text-gray-600 dark:text-gray-300">{restaurant.rating.toFixed(1)}</span>
                        </div>
                      )}

                      {restaurant.address && (
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{restaurant.address}</p>
                      )}

                      <div className="mt-2">
                        <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                          isWithinBudget(restaurant.priceLevel)
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                        }`}>
                          {isWithinBudget(restaurant.priceLevel) ? 'Within Budget' : 'Above Budget'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Attractions Tab */}
        {activeTab === 'attractions' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-3">
              Recommended Attractions
            </h3>

            {attractions.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400">No attraction recommendations available.</p>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {attractions.map(attraction => (
                  <div
                    key={attraction.id}
                    className={`border rounded-lg overflow-hidden ${
                      isWithinBudget(attraction.priceLevel)
                        ? 'border-green-200 dark:border-green-900'
                        : 'border-yellow-200 dark:border-yellow-900'
                    }`}
                  >
                    {attraction.photoUrl ? (
                      <div className="h-32 overflow-hidden">
                        <img
                          src={attraction.photoUrl}
                          alt={attraction.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className="h-32 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                        <span className="text-gray-400 dark:text-gray-500">No image</span>
                      </div>
                    )}

                    <div className="p-3">
                      <div className="flex justify-between items-start mb-1">
                        <h4 className="font-medium text-gray-800 dark:text-white">{attraction.name}</h4>
                        {attraction.priceLevel !== undefined && (
                          <span className={`text-sm font-medium ${
                            isWithinBudget(attraction.priceLevel)
                              ? 'text-green-600 dark:text-green-400'
                              : 'text-yellow-600 dark:text-yellow-400'
                          }`}>
                            {getPriceLevel(attraction.priceLevel)}
                          </span>
                        )}
                      </div>

                      {attraction.rating && (
                        <div className="flex items-center mb-1">
                          <span className="text-yellow-500 mr-1">★</span>
                          <span className="text-sm text-gray-600 dark:text-gray-300">{attraction.rating.toFixed(1)}</span>
                        </div>
                      )}

                      {attraction.address && (
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{attraction.address}</p>
                      )}

                      {attraction.priceLevel !== undefined && (
                        <div className="mt-2">
                          <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                            isWithinBudget(attraction.priceLevel)
                              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                              : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                          }`}>
                            {isWithinBudget(attraction.priceLevel) ? 'Within Budget' : 'Above Budget'}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
