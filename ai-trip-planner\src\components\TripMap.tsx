"use client";

import { useState } from 'react';
import CustomMapView from './CustomMapView';

// Define types for map points of interest
export interface MapLocation {
  id: string;
  type: 'hotel' | 'restaurant' | 'attraction';
  name: string;
  position: {
    lat: number;
    lng: number;
  };
  priceLevel?: number; // 1-4, where 1 is least expensive
  rating?: number; // 1-5
  address?: string;
  photoUrl?: string;
}

interface TripMapProps {
  destination: string;
  locations: MapLocation[];
  budget: number;
}

export default function TripMap({ destination, locations, budget }: TripMapProps) {
  // Use our custom map view component instead of Google Maps
  return <CustomMapView destination={destination} locations={locations} budget={budget} />;
}
