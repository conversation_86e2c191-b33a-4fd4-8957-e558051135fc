import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Trip from '@/models/Trip';
import * as localStorageService from '@/lib/localStorageService';

// GET handler to retrieve a specific trip by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Try MongoDB first
    try {
      await dbConnect();
      const trip = await Trip.findById(params.id);

      if (trip) {
        return NextResponse.json({ trip }, { status: 200 });
      }
    } catch (mongoError) {
      console.error('MongoDB error, falling back to local storage:', mongoError);
    }

    // Fallback to local storage
    const trip = localStorageService.getTripById(params.id);

    if (!trip) {
      return NextResponse.json(
        { error: 'Trip not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ trip }, { status: 200 });
  } catch (error) {
    console.error('Error fetching trip:', error);
    return NextResponse.json(
      { error: 'Failed to fetch trip' },
      { status: 500 }
    );
  }
}

// DELETE handler to remove a trip
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    let deleted = false;

    // Try MongoDB first
    try {
      await dbConnect();
      const trip = await Trip.findByIdAndDelete(params.id);
      if (trip) {
        deleted = true;
      }
    } catch (mongoError) {
      console.error('MongoDB error, falling back to local storage:', mongoError);
    }

    // If MongoDB delete failed or wasn't available, try local storage
    if (!deleted) {
      deleted = localStorageService.deleteTrip(params.id);
    }

    if (!deleted) {
      return NextResponse.json(
        { error: 'Trip not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Trip deleted successfully' }, { status: 200 });
  } catch (error) {
    console.error('Error deleting trip:', error);
    return NextResponse.json(
      { error: 'Failed to delete trip' },
      { status: 500 }
    );
  }
}
