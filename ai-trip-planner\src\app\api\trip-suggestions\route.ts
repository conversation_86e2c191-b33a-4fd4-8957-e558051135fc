import { NextRequest, NextResponse } from 'next/server';
import { TripFormData } from '@/components/TripForm';
import { TripSuggestion, TripDay } from '@/components/TripResults';
import { MapLocation } from '@/components/TripMap';

export async function POST(request: NextRequest) {
  try {
    const formData: TripFormData = await request.json();
    console.log('Received form data:', formData);
    const { budget, duration, travelers, startingLocation, interests } = formData;

    // Validate required fields
    if (budget === undefined || duration === undefined || travelers === undefined) {
      console.error('Missing required fields:', { budget, duration, travelers });
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Generate a trip suggestion based on the form data
    const suggestion = generateTripSuggestion(budget, duration, travelers, startingLocation, interests);
    console.log('Generated suggestion for destination:', suggestion.destination);

    // Generate location recommendations for maps and place listings
    const locations = generateLocationRecommendations(suggestion.destination, budget, interests);
    console.log('Generated locations count:', locations.length);

    return NextResponse.json({ suggestion, locations }, { status: 200 });
  } catch (error) {
    console.error('Error generating trip suggestion:', error);
    return NextResponse.json(
      { error: 'Failed to generate trip suggestion' },
      { status: 500 }
    );
  }
}

function generateTripSuggestion(
  budget: number,
  duration: number,
  travelers: number,
  startingLocation: string,
  interests: string
): TripSuggestion {
  // This is a simplified mock implementation
  // In a real app, this would use an AI service or more sophisticated logic

  // Determine destination based on interests and starting location
  const destinations = [
    'Paris, France',
    'Tokyo, Japan',
    'New York, USA',
    'Bali, Indonesia',
    'Rome, Italy',
    'Barcelona, Spain',
    'Cancun, Mexico',
    'Sydney, Australia',
    'London, UK',
    'Bangkok, Thailand'
  ];

  // Simple algorithm to pick a destination
  const destinationIndex = Math.floor(
    (budget % 1000) / 100 + (duration % 10) + startingLocation.length % 10
  ) % destinations.length;

  const destination = destinations[destinationIndex];

  // Calculate daily budget per person
  const dailyBudgetPerPerson = budget / (duration * travelers);

  // Calculate total daily budget for the group
  const dailyBudget = dailyBudgetPerPerson * travelers;

  // Generate itinerary
  const itinerary: TripDay[] = [];

  for (let day = 1; day <= duration; day++) {
    // Allocate costs based on daily budget
    const accommodationCost = Math.round(dailyBudget * 0.4);
    const mealsCost = Math.round(dailyBudget * 0.2);
    const activitiesCost = Math.round(dailyBudget * 0.3);
    const transportationCost = Math.round(dailyBudget * 0.1);

    // Generate activities based on interests
    const activities = generateActivities(destination, interests, day);

    // Generate meals
    const meals = [
      `Breakfast: Local café (${Math.round(mealsCost * 0.2)} USD)`,
      `Lunch: ${day % 2 === 0 ? 'Street food' : 'Restaurant'} (${Math.round(mealsCost * 0.3)} USD)`,
      `Dinner: ${day % 3 === 0 ? 'Fine dining' : 'Casual restaurant'} (${Math.round(mealsCost * 0.5)} USD)`
    ];

    // Generate accommodation
    const accommodation = generateAccommodation(destination, accommodationCost);

    itinerary.push({
      day,
      activities,
      accommodation,
      meals,
      costs: {
        activities: activitiesCost,
        accommodation: accommodationCost,
        meals: mealsCost,
        transportation: transportationCost
      }
    });
  }

  // Calculate total cost
  const totalCost = itinerary.reduce(
    (sum, day) =>
      sum +
      day.costs.activities +
      day.costs.accommodation +
      day.costs.meals +
      day.costs.transportation,
    0
  );

  // Generate summary
  const summary = generateSummary(destination, duration, travelers, interests, totalCost);

  return {
    destination,
    summary,
    totalCost,
    itinerary
  };
}

function generateActivities(destination: string, interests: string, day: number): string[] {
  // This is a simplified implementation
  // In a real app, this would use more sophisticated logic or AI

  const commonActivities = [
    'Visit local museum',
    'Explore city center',
    'Shopping at local markets',
    'Take a guided tour',
    'Visit historical landmarks',
    'Relax at a park',
    'Try local cuisine'
  ];

  const beachActivities = [
    'Relax on the beach',
    'Go swimming',
    'Try water sports',
    'Beach volleyball',
    'Sunset beach walk'
  ];

  const foodActivities = [
    'Food tour',
    'Cooking class',
    'Visit food markets',
    'Wine tasting',
    'Dine at a famous restaurant'
  ];

  const adventureActivities = [
    'Hiking',
    'Zip-lining',
    'Rock climbing',
    'Kayaking',
    'Mountain biking'
  ];

  const culturalActivities = [
    'Visit art galleries',
    'Attend local performance',
    'Visit historical sites',
    'Cultural workshop',
    'Visit religious sites'
  ];

  // Select activities based on interests
  let availableActivities = [...commonActivities];

  if (interests.toLowerCase().includes('beach')) {
    availableActivities = [...availableActivities, ...beachActivities];
  }

  if (interests.toLowerCase().includes('food')) {
    availableActivities = [...availableActivities, ...foodActivities];
  }

  if (interests.toLowerCase().includes('adventure')) {
    availableActivities = [...availableActivities, ...adventureActivities];
  }

  if (interests.toLowerCase().includes('culture') || interests.toLowerCase().includes('museum')) {
    availableActivities = [...availableActivities, ...culturalActivities];
  }

  // Shuffle and select 3-4 activities for the day
  const shuffled = availableActivities.sort(() => 0.5 - Math.random());
  const numActivities = 3 + (day % 2); // 3 or 4 activities

  return shuffled.slice(0, numActivities);
}

function generateAccommodation(destination: string, budget: number): string {
  // Simple logic to determine accommodation type based on budget
  if (budget > 200) {
    return `Luxury hotel in ${destination} (${budget} USD)`;
  } else if (budget > 100) {
    return `Mid-range hotel in ${destination} (${budget} USD)`;
  } else if (budget > 50) {
    return `Budget hotel in ${destination} (${budget} USD)`;
  } else {
    return `Hostel in ${destination} (${budget} USD)`;
  }
}

function generateSummary(destination: string, duration: number, travelers: number, interests: string, totalCost: number): string {
  const interestsList = interests
    .split(',')
    .map(i => i.trim())
    .filter(i => i.length > 0)
    .join(', ');

  const travelerText = travelers === 1 ? 'solo traveler' : `group of ${travelers} travelers`;
  return `A ${duration}-day trip to ${destination} for a ${travelerText} focusing on ${interestsList || 'exploring the destination'}. This itinerary includes accommodations, meals, activities, and local transportation for a total estimated cost of $${totalCost}.`;
}

function generateLocationRecommendations(destination: string, budget: number, interests: string): MapLocation[] {
  // This is a mock implementation
  // In a real app, this would use a real API like Google Places API

  const locations: MapLocation[] = [];

  // Get destination coordinates
  const destinationCoords = getDestinationCoordinates(destination);

  // Generate hotels
  const hotels = [
    {
      name: 'Luxury Grand Hotel',
      priceLevel: 4,
      rating: 4.8,
      address: `123 Main St, ${destination}`,
      photoUrl: 'https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
      position: {
        lat: destinationCoords.lat + 0.01,
        lng: destinationCoords.lng + 0.01
      }
    },
    {
      name: 'Comfort Inn',
      priceLevel: 3,
      rating: 4.2,
      address: `456 Center Ave, ${destination}`,
      photoUrl: 'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
      position: {
        lat: destinationCoords.lat - 0.01,
        lng: destinationCoords.lng + 0.02
      }
    },
    {
      name: 'Budget Stay',
      priceLevel: 2,
      rating: 3.9,
      address: `789 Budget Blvd, ${destination}`,
      photoUrl: 'https://images.unsplash.com/photo-1512918728675-ed5a9ecdebfd?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
      position: {
        lat: destinationCoords.lat + 0.02,
        lng: destinationCoords.lng - 0.01
      }
    },
    {
      name: 'Backpacker Hostel',
      priceLevel: 1,
      rating: 4.0,
      address: `101 Traveler St, ${destination}`,
      photoUrl: 'https://images.unsplash.com/photo-1555854877-bab0e564b8d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
      position: {
        lat: destinationCoords.lat - 0.02,
        lng: destinationCoords.lng - 0.02
      }
    }
  ];

  // Generate restaurants and cafes
  const restaurants = [
    {
      name: 'Gourmet Delight',
      priceLevel: 4,
      rating: 4.7,
      address: `234 Culinary Ave, ${destination}`,
      photoUrl: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
      position: {
        lat: destinationCoords.lat + 0.015,
        lng: destinationCoords.lng + 0.025
      }
    },
    {
      name: 'Cafe Central',
      priceLevel: 2,
      rating: 4.5,
      address: `567 Coffee St, ${destination}`,
      photoUrl: 'https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
      position: {
        lat: destinationCoords.lat - 0.015,
        lng: destinationCoords.lng + 0.01
      }
    },
    {
      name: 'Local Eats',
      priceLevel: 2,
      rating: 4.3,
      address: `890 Flavor Rd, ${destination}`,
      photoUrl: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
      position: {
        lat: destinationCoords.lat,
        lng: destinationCoords.lng + 0.03
      }
    },
    {
      name: 'Street Food Corner',
      priceLevel: 1,
      rating: 4.4,
      address: `321 Market St, ${destination}`,
      photoUrl: 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
      position: {
        lat: destinationCoords.lat - 0.025,
        lng: destinationCoords.lng - 0.01
      }
    }
  ];

  // Generate attractions
  const attractions = [
    {
      name: 'Famous Museum',
      priceLevel: 2,
      rating: 4.6,
      address: `432 Culture St, ${destination}`,
      photoUrl: 'https://images.unsplash.com/photo-1582034986517-30d163aa1da9?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
      position: {
        lat: destinationCoords.lat + 0.005,
        lng: destinationCoords.lng - 0.025
      }
    },
    {
      name: 'Historic Site',
      priceLevel: 1,
      rating: 4.4,
      address: `765 History Ave, ${destination}`,
      photoUrl: 'https://images.unsplash.com/photo-1569949381669-ecf31ae8e613?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
      position: {
        lat: destinationCoords.lat - 0.005,
        lng: destinationCoords.lng + 0.015
      }
    },
    {
      name: 'City Park',
      priceLevel: 0,
      rating: 4.5,
      address: `098 Nature Blvd, ${destination}`,
      photoUrl: 'https://images.unsplash.com/photo-1519331379826-f10be5486c6f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
      position: {
        lat: destinationCoords.lat + 0.02,
        lng: destinationCoords.lng + 0.005
      }
    },
    {
      name: 'Shopping District',
      priceLevel: 3,
      rating: 4.2,
      address: `543 Retail Rd, ${destination}`,
      photoUrl: 'https://images.unsplash.com/photo-1555529669-e69e7aa0ba9a?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',
      position: {
        lat: destinationCoords.lat - 0.01,
        lng: destinationCoords.lng - 0.03
      }
    }
  ];

  // Add hotels to locations
  hotels.forEach((hotel, index) => {
    locations.push({
      id: `hotel-${index}`,
      type: 'hotel',
      name: hotel.name,
      position: hotel.position,
      priceLevel: hotel.priceLevel,
      rating: hotel.rating,
      address: hotel.address,
      photoUrl: hotel.photoUrl
    });
  });

  // Add restaurants to locations
  restaurants.forEach((restaurant, index) => {
    locations.push({
      id: `restaurant-${index}`,
      type: 'restaurant',
      name: restaurant.name,
      position: restaurant.position,
      priceLevel: restaurant.priceLevel,
      rating: restaurant.rating,
      address: restaurant.address,
      photoUrl: restaurant.photoUrl
    });
  });

  // Add attractions to locations
  attractions.forEach((attraction, index) => {
    locations.push({
      id: `attraction-${index}`,
      type: 'attraction',
      name: attraction.name,
      position: attraction.position,
      priceLevel: attraction.priceLevel,
      rating: attraction.rating,
      address: attraction.address,
      photoUrl: attraction.photoUrl
    });
  });

  return locations;
}

// Helper function to get coordinates for a destination
function getDestinationCoordinates(dest: string): { lat: number; lng: number } {
  // In a real app, you would use the Geocoding API
  // This is just a mock implementation with some hardcoded values
  const destinations: Record<string, { lat: number; lng: number }> = {
    'Paris, France': { lat: 48.8566, lng: 2.3522 },
    'Tokyo, Japan': { lat: 35.6762, lng: 139.6503 },
    'New York, USA': { lat: 40.7128, lng: -74.0060 },
    'Bali, Indonesia': { lat: -8.4095, lng: 115.1889 },
    'Rome, Italy': { lat: 41.9028, lng: 12.4964 },
    'Barcelona, Spain': { lat: 41.3851, lng: 2.1734 },
    'Cancun, Mexico': { lat: 21.1619, lng: -86.8515 },
    'Sydney, Australia': { lat: -33.8688, lng: 151.2093 },
    'London, UK': { lat: 51.5074, lng: -0.1278 },
    'Bangkok, Thailand': { lat: 13.7563, lng: 100.5018 }
  };

  return destinations[dest] || { lat: 0, lng: 0 };
}
